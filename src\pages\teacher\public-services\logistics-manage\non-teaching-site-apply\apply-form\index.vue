<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUserStore } from '@/store/user'
import FormContainer from '@/components/common/FormContainer.vue'
import FormItem from '@/components/common/FormItem.vue'
import FormLabel from '@/components/common/FormLabel.vue'
import TextPlaceholder from '@/components/common/TextPlaceholder.vue'
import TextareaInput from '@/components/common/TextareaInput.vue'

// 获取用户信息
const userStore = useUserStore()

// 表单数据
const formData = ref({
  usageTheme: '', // 使用主题
  usageDate: '2025-08-05', // 使用日期
  period: '', // 使用节次
  weekDay: '', // 星期
  remark: '', // 备注
  deadlineDate: '', // 截止日期
  deadlineWeekDay: '', // 截止日期的星期
})

// 计算属性：使用部门（从用户信息读取，只读）
const usageDepartment = computed(() => {
  return userStore.userInfo.department || '物联网与人工智能学院'
})

// 计算属性：负责人（从用户信息读取，只读）
const responsiblePerson = computed(() => {
  return userStore.userInfo.realname || '余新华'
})

// 节次选项
const periodOptions = [
  { label: '第1-2节', value: '1-2' },
  { label: '第3-4节', value: '3-4' },
  { label: '第5-6节', value: '5-6' },
  { label: '第7-8节', value: '7-8' },
  { label: '第9-10节', value: '9-10' },
  { label: '第11-12节', value: '11-12' },
]

// 星期选项
const weekDayOptions = [
  { label: '星期一', value: '1' },
  { label: '星期二', value: '2' },
  { label: '星期三', value: '3' },
  { label: '星期四', value: '4' },
  { label: '星期五', value: '5' },
  { label: '星期六', value: '6' },
  { label: '星期日', value: '7' },
]

// 处理使用主题输入
const handleThemeInput = (value: string) => {
  formData.value.usageTheme = value
}

// 处理备注输入
const handleRemarkInput = (value: string) => {
  formData.value.remark = value
}

// 处理使用日期选择
const handleUsageDateChange = (event: any) => {
  formData.value.usageDate = event.detail.value
}

// 处理截止日期选择
const handleDeadlineDateChange = (event: any) => {
  formData.value.deadlineDate = event.detail.value
}

// 处理节次选择
const handlePeriodChange = (event: any) => {
  const index = event.detail.value
  formData.value.period = periodOptions[index].value
}

// 处理星期选择
const handleWeekDayChange = (event: any) => {
  const index = event.detail.value
  formData.value.weekDay = weekDayOptions[index].value
}

// 处理截止日期星期选择
const handleDeadlineWeekDayChange = (event: any) => {
  const index = event.detail.value
  formData.value.deadlineWeekDay = weekDayOptions[index].value
}

// 获取当前选中的节次显示文本
const selectedPeriodText = computed(() => {
  const option = periodOptions.find((item) => item.value === formData.value.period)
  return option ? option.label : '请选择节次'
})

// 获取当前选中的星期显示文本
const selectedWeekDayText = computed(() => {
  const option = weekDayOptions.find((item) => item.value === formData.value.weekDay)
  return option ? option.label : '请选择星期'
})

// 获取当前选中的截止日期星期显示文本
const selectedDeadlineWeekDayText = computed(() => {
  const option = weekDayOptions.find((item) => item.value === formData.value.deadlineWeekDay)
  return option ? option.label : '请选择星期'
})

// 暴露表单数据给父组件
defineExpose({
  formData,
})
</script>

<template>
  <FormContainer>
    <!-- 使用部门 -->
    <FormItem>
      <FormLabel text="使用部门" />
      <TextPlaceholder :value="usageDepartment" />
    </FormItem>

    <!-- 使用主题 -->
    <FormItem>
      <FormLabel text="使用主题" />
      <TextareaInput
        v-model="formData.usageTheme"
        @update:modelValue="handleThemeInput"
        placeholder="请输入使用主题"
        :maxlength="200"
        :show-count="true"
        :auto-height="true"
      />
    </FormItem>

    <!-- 使用日期 -->
    <FormItem>
      <FormLabel text="使用日期" />
      <picker
        mode="date"
        :value="formData.usageDate"
        start="2023-01-01"
        end="2030-12-31"
        @change="handleUsageDateChange"
      >
        <view class="form-input p-[16rpx] bg-gray-50 rounded-lg flex items-center justify-between">
          <text class="text-sm">{{ formData.usageDate || '请选择使用日期' }}</text>
          <text class="text-gray-400">📅</text>
        </view>
      </picker>
    </FormItem>

    <!-- 使用节次 -->
    <FormItem>
      <FormLabel text="使用节次" />
      <picker mode="selector" :range="periodOptions" range-key="label" @change="handlePeriodChange">
        <view class="form-input p-[16rpx] bg-gray-50 rounded-lg flex items-center justify-between">
          <text class="text-sm">{{ selectedPeriodText }}</text>
          <text class="text-gray-400">▼</text>
        </view>
      </picker>
    </FormItem>

    <!-- 星期 -->
    <FormItem>
      <FormLabel text="星期" />
      <picker
        mode="selector"
        :range="weekDayOptions"
        range-key="label"
        @change="handleWeekDayChange"
      >
        <view class="form-input p-[16rpx] bg-gray-50 rounded-lg flex items-center justify-between">
          <text class="text-sm">{{ selectedWeekDayText }}</text>
          <text class="text-gray-400">▼</text>
        </view>
      </picker>
    </FormItem>

    <!-- 备注 -->
    <FormItem>
      <FormLabel text="备注" />
      <TextareaInput
        v-model="formData.remark"
        @update:modelValue="handleRemarkInput"
        placeholder="请输入备注信息"
        :maxlength="500"
        :show-count="true"
        :auto-height="true"
      />
    </FormItem>

    <!-- 负责人 -->
    <FormItem>
      <FormLabel text="负责人" />
      <TextPlaceholder :value="responsiblePerson" />
    </FormItem>

    <!-- 批量申请配置 -->
    <FormItem>
      <FormLabel text="批量申请配置" />
      <view class="batch-config bg-gray-50 rounded-lg p-[16rpx]">
        <!-- 截止日期 -->
        <view class="config-item mb-[16rpx]">
          <text class="config-label text-sm text-gray-600 mb-[8rpx] block">截止日期</text>
          <picker
            mode="date"
            :value="formData.deadlineDate"
            start="2023-01-01"
            end="2030-12-31"
            @change="handleDeadlineDateChange"
          >
            <view
              class="form-input p-[12rpx] bg-white rounded-lg flex items-center justify-between"
            >
              <text class="text-sm">{{ formData.deadlineDate || '请选择截止日期' }}</text>
              <text class="text-gray-400">📅</text>
            </view>
          </picker>
        </view>

        <!-- 星期 -->
        <view class="config-item">
          <text class="config-label text-sm text-gray-600 mb-[8rpx] block">星期</text>
          <picker
            mode="selector"
            :range="weekDayOptions"
            range-key="label"
            @change="handleDeadlineWeekDayChange"
          >
            <view
              class="form-input p-[12rpx] bg-white rounded-lg flex items-center justify-between"
            >
              <text class="text-sm">{{ selectedDeadlineWeekDayText }}</text>
              <text class="text-gray-400">▼</text>
            </view>
          </picker>
        </view>
      </view>
    </FormItem>
  </FormContainer>
</template>

<style lang="scss" scoped>
.batch-config {
  .config-item {
    .config-label {
      font-weight: 500;
    }
  }
}
</style>
